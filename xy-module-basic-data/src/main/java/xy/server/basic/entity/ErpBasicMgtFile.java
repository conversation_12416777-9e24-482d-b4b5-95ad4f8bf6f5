package xy.server.basic.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.xunyue.common.util.JsonbTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 文件管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "erp_basic_mgt_file", autoResultMap = true)
public class ErpBasicMgtFile implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId("file_guid")
    private String fileGuid;
    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;
    /**
     * 租户guid
     */
    @TableField("tenant_guid")
    private String tenantGuid;
    /**
     * ERP_FileMGT_Folder GUID
     */
    @TableField("folder_guid")
    private String folderGuid;
    /**
     * 文件路径
     */
    @TableField("path")
    private String path;
    /**
     * 备注或描述
     */
    @TableField("description")
    private String description;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人GUID
     */
    @TableField(value = "creator_guid", fill = FieldFill.INSERT)
    private String creatorGuid;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建日期
     */
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private LocalDateTime createDate;

    /**
     * 最后修改人GUID
     */
    @TableField(value = "last_updater_guid", fill = FieldFill.INSERT_UPDATE)
    private String lastUpdaterGuid;

    /**
     * 最后修改人
     */
    @TableField(value = "last_updater", fill = FieldFill.INSERT_UPDATE)
    private String lastUpdater;

    /**
     * 最后修改日期
     */
    @TableField(value = "last_update_date", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdateDate;

    /**
     * 备用字段
     */
    @TableField(value = "to_json", typeHandler = JsonbTypeHandler.class)
    private Object toJson;

}
