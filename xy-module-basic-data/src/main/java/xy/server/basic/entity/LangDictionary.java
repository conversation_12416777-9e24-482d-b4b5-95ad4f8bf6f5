package xy.server.basic.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 语言字典
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("lang_dictionary")
public class LangDictionary implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId("lang_dictionary_guid")
    private String langDictionaryGuid;

    @TableField("tenant_guid")
    private String tenantGuid;
    /**
     * 平台
     */
    @TableField("lang_dictionary_plat")
    private String langDictionaryPlat;
    /**
     * 语言标识
     */
    @TableField("lang_dictionary_locale")
    private String langDictionaryLocale;
    /**
     * 值
     */
    @TableField(value = "lang_dictionary_value", typeHandler = FastjsonTypeHandler.class)
    private JSONObject langDictionaryValue;


}
