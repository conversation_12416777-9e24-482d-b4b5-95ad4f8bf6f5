package xy.server.policy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunyue.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xy.server.policy.common.enums.NodeComponentTypeEnum;
import xy.server.policy.entity.*;
import xy.server.policy.entity.model.ro.ErpPolicyEngineNodeRO;
import xy.server.policy.mapper.ErpPolicyEngineNodeMapper;
import xy.server.policy.service.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 决策节点流程 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service
public class ErpPolicyEngineNodeServiceImpl extends ServiceImpl<ErpPolicyEngineNodeMapper, ErpPolicyEngineNode> implements IErpPolicyEngineNodeService {

    private final IErpPolicyEngineNodeMetaSqlService iErpPolicyEngineNodeMetaSqlService;
    private final IErpPolicyEngineNodeMetaNotifyService iErpPolicyEngineNodeMetaNotifyService;
    private final IErpPolicyEngineNodeMetaBroadcastService iErpPolicyEngineNodeMetaBroadcastService;
    private final IErpPolicyEngineNodeMetaApiService iErpPolicyEngineNodeMetaApiService;
    private final IErpPolicyEngineNodeMetaDelayService iErpPolicyEngineNodeMetaDelayService;
    /**
     * 保存数据
     * @param policyEngineGuid 决策引擎GUID
     * @param policyEngineNodeList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(String policyEngineGuid, List<ErpPolicyEngineNodeRO> policyEngineNodeList) {
        // 先删除旧数据（物理删除）
        this.delByPolicyEngineGuid(policyEngineGuid);
        // 再保存新数据
        if (CollectionUtil.isNotEmpty(policyEngineNodeList)) {
            List<ErpPolicyEngineNode> entityList = new ArrayList<>(policyEngineNodeList.size());
            List<ErpPolicyEngineNodeMetaSql> nodeMetaSqlList = new ArrayList();
            List<ErpPolicyEngineNodeMetaNotify> nodeMetaNotifyList = new ArrayList();
            List<ErpPolicyEngineNodeMetaBroadcast> nodeMetaBroadcastList = new ArrayList();
            List<ErpPolicyEngineNodeMetaApi> nodeMetaApiList = new ArrayList();
            List<ErpPolicyEngineNodeMetaDelay> nodeMetaDelayList = new ArrayList();
            policyEngineNodeList.forEach(item -> {
                ErpPolicyEngineNode entity = BeanUtil.copyProperties(item, ErpPolicyEngineNode.class);
                entity.setPolicyEngineGuid(policyEngineGuid);
                entity.setPolicyEngineNodeGuid(StringUtils.isNotBlank(entity.getPolicyEngineNodeGuid()) ? entity.getPolicyEngineNodeGuid() : IdUtil.fastSimpleUUID());
                entityList.add(entity);
                // 根据节点类型处理扩展表
                switch (NodeComponentTypeEnum.getEumByCode(item.getComponentType())) {
                    case SQL:
                        ErpPolicyEngineNodeMetaSql nodeMetaSql = BeanUtil.copyProperties(item.getPolicyEngineNodeMetaSqlObj(), ErpPolicyEngineNodeMetaSql.class);
                        nodeMetaSql.setPolicyEngineNodeGuid(entity.getPolicyEngineNodeGuid());
                        nodeMetaSqlList.add(nodeMetaSql);
                        break;
                    case MESSAGE_NOTIFICATION:
                        ErpPolicyEngineNodeMetaNotify nodeMetaNotify = BeanUtil.copyProperties(item.getPolicyEngineNodeMetaNotifyObj(), ErpPolicyEngineNodeMetaNotify.class);
                        nodeMetaNotify.setPolicyEngineNodeGuid(entity.getPolicyEngineNodeGuid());
                        nodeMetaNotifyList.add(nodeMetaNotify);
                        break;
                    case PROCESS_PUSH_DOWN:
                    case BROADCAST:
                        ErpPolicyEngineNodeMetaBroadcast nodeMetaBroadcast = BeanUtil.copyProperties(item.getPolicyEngineNodeMetaBroadcastObj(), ErpPolicyEngineNodeMetaBroadcast.class);
                        nodeMetaBroadcast.setPolicyEngineNodeGuid(entity.getPolicyEngineNodeGuid());
                        nodeMetaBroadcastList.add(nodeMetaBroadcast);
                        break;
                    case API_REQUEST:
                        ErpPolicyEngineNodeMetaApi nodeMetaApi = BeanUtil.copyProperties(item.getPolicyEngineNodeMetaApiObj(), ErpPolicyEngineNodeMetaApi.class);
                        nodeMetaApi.setPolicyEngineNodeGuid(entity.getPolicyEngineNodeGuid());
                        nodeMetaApiList.add(nodeMetaApi);
                        break;
                    case DELAY_EXPIRE:
                    case DELAY_OVERDUE:
                        ErpPolicyEngineNodeMetaDelay nodeMetaDelay = BeanUtil.copyProperties(item.getPolicyEngineNodeMetaDelayObj(), ErpPolicyEngineNodeMetaDelay.class);
                        nodeMetaDelay.setPolicyEngineNodeGuid(entity.getPolicyEngineNodeGuid());
                        nodeMetaDelayList.add(nodeMetaDelay);
                        break;
                    default:
                        break;
                }
            });
            // 批量保存主表数据
            super.saveBatch(entityList);
            // 批量保存扩展表数据
            iErpPolicyEngineNodeMetaSqlService.saveBatch(nodeMetaSqlList);
            iErpPolicyEngineNodeMetaNotifyService.saveBatch(nodeMetaNotifyList);
            iErpPolicyEngineNodeMetaBroadcastService.saveBatch(nodeMetaBroadcastList);
            iErpPolicyEngineNodeMetaApiService.saveBatch(nodeMetaApiList);
            iErpPolicyEngineNodeMetaDelayService.saveBatch(nodeMetaDelayList);

        }
    }

    /**
     * 根据决策引擎GUID删除数据
     * @param policyEngineGuid
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByPolicyEngineGuid(String policyEngineGuid) {
        // 查询所有要删除的节点guids
        List<String> policyEngineNodeGuids = baseMapper.selectList(Wrappers.<ErpPolicyEngineNode>lambdaQuery()
                        .eq(ErpPolicyEngineNode::getPolicyEngineGuid, policyEngineGuid))
                .stream().map(ErpPolicyEngineNode::getPolicyEngineNodeGuid).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(policyEngineNodeGuids)) {
            // 删除节点主表数据
            super.removeBatchByIds(policyEngineNodeGuids);
            //删除节点扩展表数据
            iErpPolicyEngineNodeMetaSqlService.remove(Wrappers.<ErpPolicyEngineNodeMetaSql>lambdaQuery()
                    .in(ErpPolicyEngineNodeMetaSql::getPolicyEngineNodeGuid, policyEngineNodeGuids));
            iErpPolicyEngineNodeMetaNotifyService.remove(Wrappers.<ErpPolicyEngineNodeMetaNotify>lambdaQuery()
                    .in(ErpPolicyEngineNodeMetaNotify::getPolicyEngineNodeGuid, policyEngineNodeGuids));
            iErpPolicyEngineNodeMetaBroadcastService.remove(Wrappers.<ErpPolicyEngineNodeMetaBroadcast>lambdaQuery()
                    .in(ErpPolicyEngineNodeMetaBroadcast::getPolicyEngineNodeGuid, policyEngineNodeGuids));
            iErpPolicyEngineNodeMetaApiService.remove(Wrappers.<ErpPolicyEngineNodeMetaApi>lambdaQuery()
                    .in(ErpPolicyEngineNodeMetaApi::getPolicyEngineNodeGuid, policyEngineNodeGuids));
            iErpPolicyEngineNodeMetaDelayService.remove(Wrappers.<ErpPolicyEngineNodeMetaDelay>lambdaQuery()
                    .in(ErpPolicyEngineNodeMetaDelay::getPolicyEngineNodeGuid, policyEngineNodeGuids));
        }
    }
}
